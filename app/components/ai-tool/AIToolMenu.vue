<script setup lang="ts">
import type { NavigationMenuItem } from '@nuxt/ui'

const { t } = useI18n()
const route = useRoute()

// Animation states for each menu item
const hoveredItem = ref<string | null>(null)
const isAnimating = ref(false)

const items = computed(
  () =>
    [
      {
        id: 'imagen',
        label: t('aiToolMenu.imagen'),
        icon: 'hugeicons:ai-image',
        slot: 'image' as const,
        to: '/app',
        exact: true,
        color: 'secondary',
        bgGradient: 'from-purple-500/20 via-pink-500/20 to-blue-500/20',
        description: t('Generate stunning images with AI')
      },
      {
        id: 'video-gen',
        label: t('aiToolMenu.videoGen'),
        icon: 'hugeicons:ai-video',
        slot: 'components' as const,
        to: '/app/video-gen',
        color: 'success',
        bgGradient: 'from-green-500/20 via-emerald-500/20 to-teal-500/20',
        description: t('Create amazing videos from text or images')
      },
      {
        id: 'speech-gen',
        label: t('aiToolMenu.speechGen'),
        icon: 'hugeicons:ai-voice',
        slot: 'components' as const,
        to: '/app/speech-gen',
        color: 'warning',
        bgGradient: 'from-orange-500/20 via-amber-500/20 to-yellow-500/20',
        description: t('Convert text to natural speech')
      },
      {
        id: 'dialogue-gen',
        label: t('speech.dialogueGeneration.dialogueGen'),
        icon: 'ri:chat-smile-ai-line',
        slot: 'components' as const,
        to: '/app/dialogue-gen',
        color: 'error',
        bgGradient: 'from-red-500/20 via-rose-500/20 to-pink-500/20',
        description: t('Generate conversations between multiple speakers')
      }
    ] satisfies (NavigationMenuItem & {
      id: string
      bgGradient: string
      description: string
    })[]
)

const switchMenu = (item: any) => {
  isAnimating.value = true
  setTimeout(() => {
    navigateTo(item.to)
    isAnimating.value = false
  }, 300)
}

const handleMouseEnter = (itemId: string) => {
  hoveredItem.value = itemId
}

const handleMouseLeave = () => {
  hoveredItem.value = null
}

// Generate floating particles for each menu type
const generateParticles = (count: number) => {
  return Array.from({ length: count }, (_, i) => ({
    id: i,
    delay: Math.random() * 2,
    duration: 3 + Math.random() * 2,
    x: Math.random() * 100,
    y: Math.random() * 100,
    scale: 0.5 + Math.random() * 0.5
  }))
}

const imageParticles = generateParticles(8)
const videoParticles = generateParticles(6)
const speechParticles = generateParticles(10)
const dialogueParticles = generateParticles(12)
</script>

<template>
  <div class="w-fit mx-auto">
    <div class="grid grid-cols-2 lg:grid-cols-4 gap-6 p-4">
      <!-- Imagen Menu -->
      <div
        class="relative group cursor-pointer"
        @mouseenter="handleMouseEnter('imagen')"
        @mouseleave="handleMouseLeave"
        @click="switchMenu(items[0])"
      >
        <div
          class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-500/10 via-pink-500/10 to-blue-500/10 backdrop-blur-sm border border-purple-200/20 dark:border-purple-500/20 p-6 h-32 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-purple-500/25"
          :class="[
            route.path === items[0].to ? 'scale-105 shadow-2xl shadow-purple-500/25 ring-2 ring-purple-500/50' : '',
            hoveredItem === 'imagen' ? 'bg-gradient-to-br from-purple-500/20 via-pink-500/20 to-blue-500/20' : ''
          ]"
        >
          <!-- Floating Image Particles -->
          <div
            v-for="particle in imageParticles"
            :key="particle.id"
            class="absolute opacity-30 transition-all duration-1000"
            :class="[
              hoveredItem === 'imagen' ? 'opacity-60 scale-110' : 'opacity-20'
            ]"
            :style="{
              left: particle.x + '%',
              top: particle.y + '%',
              animationDelay: particle.delay + 's',
              animationDuration: particle.duration + 's',
              transform: `scale(${particle.scale})`
            }"
          >
            <div class="w-3 h-3 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse" />
          </div>

          <!-- Background Pattern -->
          <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-br from-purple-100/50 to-pink-100/50 animate-pulse" />
          </div>

          <!-- Content -->
          <div class="relative z-10 flex flex-col items-center justify-center h-full text-center">
            <UIcon
              :name="items[0]?.icon || ''"
              class="w-8 h-8 mb-2 transition-all duration-300 group-hover:scale-125 group-hover:rotate-12"
              :class="[
                route.path === items[0]?.to ? 'text-purple-500 scale-125' : 'text-gray-600 dark:text-gray-300',
                hoveredItem === 'imagen' ? 'text-purple-500 scale-125 rotate-12' : ''
              ]"
            />
            <div
              class="text-sm font-semibold transition-all duration-300"
              :class="[
                route.path === items[0]?.to ? 'text-purple-600 dark:text-purple-400' : 'text-gray-700 dark:text-gray-200',
                hoveredItem === 'imagen' ? 'text-purple-600 dark:text-purple-400' : ''
              ]"
            >
              {{ items[0]?.label }}
            </div>
            <div
              class="text-xs text-gray-500 dark:text-gray-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            >
              {{ items[0]?.description }}
            </div>
          </div>

          <!-- Hover Glow Effect -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-purple-500/0 via-purple-500/10 to-pink-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"
          />
        </div>
      </div>

      <!-- Video Gen Menu -->
      <div
        class="relative group cursor-pointer"
        @mouseenter="handleMouseEnter('video-gen')"
        @mouseleave="handleMouseLeave"
        @click="switchMenu(items[1])"
      >
        <div
          class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-500/10 via-emerald-500/10 to-teal-500/10 backdrop-blur-sm border border-green-200/20 dark:border-green-500/20 p-6 h-32 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-green-500/25"
          :class="[
            route.path === items[1].to ? 'scale-105 shadow-2xl shadow-green-500/25 ring-2 ring-green-500/50' : '',
            hoveredItem === 'video-gen' ? 'bg-gradient-to-br from-green-500/20 via-emerald-500/20 to-teal-500/20' : ''
          ]"
        >
          <!-- Animated Video Frames -->
          <div
            v-for="particle in videoParticles"
            :key="particle.id"
            class="absolute transition-all duration-1000"
            :class="[
              hoveredItem === 'video-gen' ? 'opacity-60 animate-bounce' : 'opacity-20'
            ]"
            :style="{
              left: particle.x + '%',
              top: particle.y + '%',
              animationDelay: particle.delay + 's',
              animationDuration: particle.duration + 's'
            }"
          >
            <div class="w-4 h-3 bg-gradient-to-r from-green-400 to-teal-400 rounded-sm opacity-60" />
          </div>

          <!-- Moving Gradient Background -->
          <div class="absolute inset-0 opacity-20">
            <div class="absolute inset-0 bg-gradient-to-r from-green-400/20 to-teal-400/20 animate-pulse" />
          </div>

          <!-- Content -->
          <div class="relative z-10 flex flex-col items-center justify-center h-full text-center">
            <UIcon
              :name="items[1]?.icon || ''"
              class="w-8 h-8 mb-2 transition-all duration-300 group-hover:scale-125"
              :class="[
                route.path === items[1]?.to ? 'text-green-500 scale-125' : 'text-gray-600 dark:text-gray-300',
                hoveredItem === 'video-gen' ? 'text-green-500 scale-125 animate-pulse' : ''
              ]"
            />
            <div
              class="text-sm font-semibold transition-all duration-300"
              :class="[
                route.path === items[1]?.to ? 'text-green-600 dark:text-green-400' : 'text-gray-700 dark:text-gray-200',
                hoveredItem === 'video-gen' ? 'text-green-600 dark:text-green-400' : ''
              ]"
            >
              {{ items[1]?.label }}
            </div>
            <div
              class="text-xs text-gray-500 dark:text-gray-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            >
              {{ items[1]?.description }}
            </div>
          </div>

          <!-- Hover Glow Effect -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-green-500/0 via-green-500/10 to-teal-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"
          />
        </div>
      </div>

      <!-- Speech Gen Menu -->
      <div
        class="relative group cursor-pointer"
        @mouseenter="handleMouseEnter('speech-gen')"
        @mouseleave="handleMouseLeave"
        @click="switchMenu(items[2])"
      >
        <div
          class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-500/10 via-amber-500/10 to-yellow-500/10 backdrop-blur-sm border border-orange-200/20 dark:border-orange-500/20 p-6 h-32 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-orange-500/25"
          :class="[
            route.path === items[2]?.to ? 'scale-105 shadow-2xl shadow-orange-500/25 ring-2 ring-orange-500/50' : '',
            hoveredItem === 'speech-gen' ? 'bg-gradient-to-br from-orange-500/20 via-amber-500/20 to-yellow-500/20' : ''
          ]"
        >
          <!-- Floating Sound Waves -->
          <div
            v-for="particle in speechParticles"
            :key="particle.id"
            class="absolute transition-all duration-1000"
            :class="[
              hoveredItem === 'speech-gen' ? 'opacity-70 animate-ping' : 'opacity-20'
            ]"
            :style="{
              left: particle.x + '%',
              top: particle.y + '%',
              animationDelay: particle.delay + 's',
              animationDuration: particle.duration + 's'
            }"
          >
            <div class="w-2 h-6 bg-gradient-to-t from-orange-400 to-yellow-400 rounded-full opacity-60" />
          </div>

          <!-- Ripple Effect -->
          <div class="absolute inset-0 opacity-20">
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div
                class="w-16 h-16 border-2 border-orange-400/30 rounded-full animate-ping"
                :class="[
                  hoveredItem === 'speech-gen' ? 'animate-ping' : ''
                ]"
              />
              <div
                class="absolute top-2 left-2 w-12 h-12 border-2 border-amber-400/40 rounded-full animate-ping"
                style="animation-delay: 0.5s"
                :class="[
                  hoveredItem === 'speech-gen' ? 'animate-ping' : ''
                ]"
              />
            </div>
          </div>

          <!-- Content -->
          <div class="relative z-10 flex flex-col items-center justify-center h-full text-center">
            <UIcon
              :name="items[2]?.icon || ''"
              class="w-8 h-8 mb-2 transition-all duration-300 group-hover:scale-125"
              :class="[
                route.path === items[2]?.to ? 'text-orange-500 scale-125' : 'text-gray-600 dark:text-gray-300',
                hoveredItem === 'speech-gen' ? 'text-orange-500 scale-125 animate-bounce' : ''
              ]"
            />
            <div
              class="text-sm font-semibold transition-all duration-300"
              :class="[
                route.path === items[2]?.to ? 'text-orange-600 dark:text-orange-400' : 'text-gray-700 dark:text-gray-200',
                hoveredItem === 'speech-gen' ? 'text-orange-600 dark:text-orange-400' : ''
              ]"
            >
              {{ items[2]?.label }}
            </div>
            <div
              class="text-xs text-gray-500 dark:text-gray-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            >
              {{ items[2]?.description }}
            </div>
          </div>

          <!-- Hover Glow Effect -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-orange-500/0 via-orange-500/10 to-yellow-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"
          />
        </div>
      </div>

      <!-- Dialogue Gen Menu -->
      <div
        class="relative group cursor-pointer"
        @mouseenter="handleMouseEnter('dialogue-gen')"
        @mouseleave="handleMouseLeave"
        @click="switchMenu(items[3])"
      >
        <div
          class="relative overflow-hidden rounded-2xl bg-gradient-to-br from-red-500/10 via-rose-500/10 to-pink-500/10 backdrop-blur-sm border border-red-200/20 dark:border-red-500/20 p-6 h-32 transition-all duration-500 hover:scale-105 hover:shadow-2xl hover:shadow-red-500/25"
          :class="[
            route.path === items[3]?.to ? 'scale-105 shadow-2xl shadow-red-500/25 ring-2 ring-red-500/50' : '',
            hoveredItem === 'dialogue-gen' ? 'bg-gradient-to-br from-red-500/20 via-rose-500/20 to-pink-500/20' : ''
          ]"
        >
          <!-- Floating Sound Waves for Dialogue -->
          <div
            v-for="particle in dialogueParticles"
            :key="particle.id"
            class="absolute transition-all duration-1000"
            :class="[
              hoveredItem === 'dialogue-gen' ? 'opacity-70 animate-ping' : 'opacity-20'
            ]"
            :style="{
              left: particle.x + '%',
              top: particle.y + '%',
              animationDelay: particle.delay + 's',
              animationDuration: particle.duration + 's'
            }"
          >
            <div
              class="bg-gradient-to-t from-red-400 to-pink-400 rounded-full opacity-60"
              :class="[
                particle.id % 3 === 0 ? 'w-2 h-6' : particle.id % 3 === 1 ? 'w-2 h-4' : 'w-2 h-8'
              ]"
            />
          </div>

          <!-- Dual Speaker Ripple Effects -->
          <div class="absolute inset-0 opacity-20">
            <!-- Left Speaker (Avatar 1) -->
            <div class="absolute top-1/2 left-6 transform -translate-y-1/2">
              <div
                class="w-12 h-12 border-2 border-red-400/30 rounded-full animate-ping"
                :class="[
                  hoveredItem === 'dialogue-gen' ? 'animate-ping' : ''
                ]"
              />
              <div
                class="absolute top-2 left-2 w-8 h-8 border-2 border-rose-400/40 rounded-full animate-ping"
                style="animation-delay: 0.3s"
                :class="[
                  hoveredItem === 'dialogue-gen' ? 'animate-ping' : ''
                ]"
              />
            </div>

            <!-- Right Speaker (Avatar 2) -->
            <div class="absolute top-1/2 right-6 transform -translate-y-1/2">
              <div
                class="w-12 h-12 border-2 border-pink-400/30 rounded-full animate-ping"
                style="animation-delay: 0.6s"
                :class="[
                  hoveredItem === 'dialogue-gen' ? 'animate-ping' : ''
                ]"
              />
              <div
                class="absolute top-2 left-2 w-8 h-8 border-2 border-red-400/40 rounded-full animate-ping"
                style="animation-delay: 0.9s"
                :class="[
                  hoveredItem === 'dialogue-gen' ? 'animate-ping' : ''
                ]"
              />
            </div>

            <!-- Center Connection Wave -->
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div
                class="w-16 h-2 bg-gradient-to-r from-red-400/20 via-rose-400/30 to-pink-400/20 rounded-full animate-pulse"
                style="animation-delay: 0.5s"
                :class="[
                  hoveredItem === 'dialogue-gen' ? 'animate-pulse' : ''
                ]"
              />
            </div>
          </div>

          <!-- Content -->
          <div class="relative z-10 flex flex-col items-center justify-center h-full text-center">
            <UIcon
              :name="items[3]?.icon || ''"
              class="w-8 h-8 mb-2 transition-all duration-300 group-hover:scale-125"
              :class="[
                route.path === items[3]?.to ? 'text-red-500 scale-125' : 'text-gray-600 dark:text-gray-300',
                hoveredItem === 'dialogue-gen' ? 'text-red-500 scale-125 animate-pulse' : ''
              ]"
            />
            <div
              class="text-sm font-semibold transition-all duration-300"
              :class="[
                route.path === items[3]?.to ? 'text-red-600 dark:text-red-400' : 'text-gray-700 dark:text-gray-200',
                hoveredItem === 'dialogue-gen' ? 'text-red-600 dark:text-red-400' : ''
              ]"
            >
              {{ items[3]?.label }}
            </div>
            <div
              class="text-xs text-gray-500 dark:text-gray-400 mt-1 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
            >
              {{ items[3]?.description }}
            </div>
          </div>

          <!-- Hover Glow Effect -->
          <div
            class="absolute inset-0 bg-gradient-to-r from-red-500/0 via-red-500/10 to-pink-500/0 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"
          />
        </div>
      </div>
    </div>
  </div>
</template>
